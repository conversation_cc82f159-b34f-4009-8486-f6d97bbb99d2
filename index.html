<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Altron</title>

  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Noto+Sans:wght@400;700&display=swap" rel="stylesheet">
  <link href="https://fonts.googleapis.com/css2?family=JetBrains+Mono&display=swap" rel="stylesheet">

  <link rel="icon" href="/assets/icon/icon-logo-color.svg">
  <link rel="stylesheet" href="/src/index.css" />

  <script type="module" src="/src/main.js"></script>

</head>

<body>
  <nav id="nav"></nav>

  <header class="bg-slate-200 h-dvh overflow-hidden relative">
    <div class="save-area w-full flex flex-col items-center justify-between space-y-12 py-20">
      <div class="text-left z-0 self-start">
        <h1>我們創造非凡的</h1>
        <h1 class="gradient-text">
          <span id="typing" class="border-r-2 pr-1 gradient-text animate-blink"></span>
        </h1>
      </div>
      <img src="/assets/icon/logo-altron-w.svg" alt="" class=" stroke-white stroke-8 w-full mx-auto z-0">
      <img id="mainImage" src="/assets/img/kv2.png" alt="" class="absolute bottom-0 left-1/2 -translate-x-1/2 w-full h-auto max-h-dvh object-contain
         sm:w-auto sm:h-[90vh] sm:max-h-[90vh] sm:object-contain sm:left-1/2 sm:-translate-x-1/2 z-10" />
      <div class="flex flex-col md:flex-row justify-end gap-4 self-end z-20">
        <button class="bg-white backdrop-blur-md p-1 px-4 rounded-full">HRC 協作人形</button>
        <button class="bg-white/50 backdrop-blur-md p-1 px-4 rounded-full">HAC 自主移動人形</button>
        <button class="bg-white/50 backdrop-blur-md p-1 px-4 rounded-full">CAB 自主移動生物</button>
        <button class="bg-white/50 backdrop-blur-md p-1 px-4 rounded-full">ARM 機械手臂</button>
      </div>
    </div>
  </header>

  <section id="counting-section" class="save-area mx-auto py-20 ">
    <div class="text-center flex flex-col items-center space-y-6 mb-20">
      <h2>重新定義人與科技的關係<br class="hidden sm:block">更是重塑未來的選擇</h2>
      <p class="mb-20">Altron 於1970年成立，為協作型機器人和智慧視覺系統先驅<br>致力於研發和應用自動化科技，以創新的協作模式改善生產環境，協助企業邁向成功</p>
      <button href="/about.html" class="btn-primary">關於我們</button>
    </div>
    <div class="grid grid-cols-2 lg:grid-cols-4 gap-6 h-full bg-slate-50 rounded-3xl p-6">
      <div class="flex flex-col items-center justify-center h-full w-full ">
        <div class="flex items-center justify-center text-center gap-2">
          <h2 id="num" data-val="54" class="gradient-text font-mono">000</h2>
          <h2 class="gradient-text font-mono">+</h2>
        </div>
        <p>銷售國家&地區</p>
      </div>
      <div class="flex flex-col items-center justify-center h-full w-full">
        <div class="flex items-center justify-center text-start h-full gap-2">
          <h2 id="num" data-val="9000" class="gradient-text font-mono">000</h2>
          <h2 class="gradient-text font-mono">+</h2>
        </div>
        <p>銷售機器人</p>
      </div>
      <div class="flex flex-col items-center justify-center h-full w-full ">
        <div class="flex justify-center items-center gap-2">
          <h2 id="num" data-val="318" class="gradient-text font-mono">000</h2>
          <h2 class="gradient-text font-mono">+</h2>
        </div>
        <p>合作夥伴</p>
      </div>
      <div class="flex flex-col items-center justify-center h-full w-full ">
        <div class="flex items-center gap-2">
          <h2 id="num" data-val="96" class="gradient-text font-mono">000</h2>
          <h2 class="gradient-text font-mono">%</h2>
        </div>
        <p>客戶留存率</p>
      </div>
    </div>
    <div class="overflow-hidden relative w-full
      [mask-image:linear-gradient(to_right,transparent,black_20%,black_80%,transparent)]
      [-webkit-mask-image:linear-gradient(to_right,transparent,black_20%,black_80%,transparent)]">

      <div class="flex animate-marquee whitespace-nowrap py-10 gap-20">
        <div class="flex min-w-max items-center gap-20">
          <img src="/assets/icon/company-logo1.svg" class="h-8 w-auto" alt="Logo 1">
          <img src="/assets/icon/company-logo2.svg" class="h-8 w-auto" alt="Logo 2">
          <img src="/assets/icon/company-logo3.svg" class="h-8 w-auto" alt="Logo 3">
          <img src="/assets/icon/company-logo4.svg" class="h-8 w-auto" alt="Logo 4">
          <img src="/assets/icon/company-logo5.svg" class="h-8 w-auto" alt="Logo 5">
        </div>
        <div class="flex min-w-max items-center gap-20">
          <img src="/assets/icon/company-logo1.svg" class="h-8 w-auto" alt="Logo 1">
          <img src="/assets/icon/company-logo2.svg" class="h-8 w-auto" alt="Logo 2">
          <img src="/assets/icon/company-logo3.svg" class="h-8 w-auto" alt="Logo 3">
          <img src="/assets/icon/company-logo4.svg" class="h-8 w-auto" alt="Logo 4">
          <img src="/assets/icon/company-logo5.svg" class="h-8 w-auto" alt="Logo 5">
        </div>
        <div class="flex min-w-max items-center gap-20">
          <img src="/assets/icon/company-logo1.svg" class="h-8 w-auto" alt="Logo 1">
          <img src="/assets/icon/company-logo2.svg" class="h-8 w-auto" alt="Logo 2">
          <img src="/assets/icon/company-logo3.svg" class="h-8 w-auto" alt="Logo 3">
          <img src="/assets/icon/company-logo4.svg" class="h-8 w-auto" alt="Logo 4">
          <img src="/assets/icon/company-logo5.svg" class="h-8 w-auto" alt="Logo 5">
        </div>
      </div>
    </div>
  </section>

  <section class="save-area mx-auto relative overflow-visible py-20 z-50">
    <div class="text-center mb-20">
      <h2 class="pb-6">為全球企業提供<br class="hidden sm:block">高效、可靠且直覺的自動化解決方案</h2>
      <p>從商業營運到醫療照護、教育應用，我們正與夥伴共同定義智慧化的未來藍圖</p>
    </div>

    <div class="grid grid-cols-2 md:grid-cols-4 gap-4 ">

      <div data-index="0" onclick="expand(this)"
        class="card relative h-[600px] transition-all duration-700 ease-in-out">
        <img src="/assets/img/product-1.jpg" alt="" class="rounded-3xl h-full object-cover z-10">

        <div class="absolute inset-0 flex flex-col justify-between text-white p-6 z-20">
          <div class="flex flex-col ">
            <h2 class=" text-slate-50">HAC</h2>
            <p class=" text-slate-50">自主移動人形</p>
          </div>
          <div class="w-full flex justify-end items-end">
            <button data-type="icon"
              class="btn-icon group flex items-center justify-center rounded-full bg-white transition-colors duration-300">
              <svg class="w-6 h-6 transform transition-transform duration-1000 group-hover:rotate-[360deg]"
                viewBox="0 0 20 20" fill="url(#grad)" xmlns="http://www.w3.org/2000/svg">
                <defs>
                  <linearGradient id="grad" x1="0%" y1="0%" x2="100%" y2="100%">
                    <stop offset="0%" stop-color="#21DEFF" />
                    <stop offset="100%" stop-color="#E17DFF" />
                  </linearGradient>
                </defs>
                <path d="M10 0L12.3335 7.66655L20 10L12.3335 12.3335L10 20L7.66655 12.3335L0 10L7.66655 7.66655L10 0Z"
                  fill="currentColor" class="fill-[url(#grad)] group-hover: transition-colors duration-500" />
              </svg>
            </button>
          </div>
        </div>
      </div>

      <div data-index="1" onclick="expand(this)"
        class="card relative h-[600px] transition-all duration-700 ease-in-out">
        <img src="/assets/img/product-2.jpg" alt="" class="rounded-3xl h-full object-cover z-10">

        <div class="absolute inset-0 flex flex-col justify-between text-white p-6 z-20">
          <div class="flex flex-col ">
            <h2 class=" text-slate-50">HRC</h2>
            <p class=" text-slate-50">協作人形</p>
          </div>
          <div class="w-full flex justify-end items-end">
            <button data-type="icon"
              class="btn-icon group flex items-center justify-center rounded-full bg-white transition-colors duration-300">
              <svg class="w-6 h-6 transform transition-transform duration-1000 group-hover:rotate-[360deg]"
                viewBox="0 0 20 20" fill="url(#grad)" xmlns="http://www.w3.org/2000/svg">
                <defs>
                  <linearGradient id="grad" x1="0%" y1="0%" x2="100%" y2="100%">
                    <stop offset="0%" stop-color="#21DEFF" />
                    <stop offset="100%" stop-color="#E17DFF" />
                  </linearGradient>
                </defs>
                <path d="M10 0L12.3335 7.66655L20 10L12.3335 12.3335L10 20L7.66655 12.3335L0 10L7.66655 7.66655L10 0Z"
                  fill="currentColor" class="fill-[url(#grad)] group-hover: transition-colors duration-500" />
              </svg>
            </button>
          </div>
        </div>
      </div>

      <div data-index="2" onclick="expand(this)"
        class="card relative h-[600px] transition-all duration-700 ease-in-out">
        <img src="/assets/img/product-3.jpg" alt="" class="rounded-3xl h-full object-cover z-10">

        <div class="absolute inset-0 flex flex-col justify-between text-white p-6 z-20">
          <div class="flex flex-col">
            <h2 class=" text-slate-50">CAB</h2>
            <p class=" text-slate-50">自主移動生物</p>
          </div>
          <div class="w-full flex justify-end items-end">
            <button data-type="icon"
              class="btn-icon group flex items-center justify-center rounded-full bg-white transition-colors duration-300">
              <svg class="w-6 h-6 transform transition-transform duration-1000 group-hover:rotate-[360deg]"
                viewBox="0 0 20 20" fill="url(#grad)" xmlns="http://www.w3.org/2000/svg">
                <defs>
                  <linearGradient id="grad" x1="0%" y1="0%" x2="100%" y2="100%">
                    <stop offset="0%" stop-color="#21DEFF" />
                    <stop offset="100%" stop-color="#E17DFF" />
                  </linearGradient>
                </defs>
                <path d="M10 0L12.3335 7.66655L20 10L12.3335 12.3335L10 20L7.66655 12.3335L0 10L7.66655 7.66655L10 0Z"
                  fill="currentColor" class="fill-[url(#grad)] group-hover: transition-colors duration-500" />
              </svg>
            </button>
          </div>
        </div>
      </div>

      <div data-index="3" onclick="expand(this)"
        class="card relative h-[600px] transition-all duration-700 ease-in-out">
        <img src="/assets/img/product-4.jpg" alt="" class="rounded-3xl object-cover h-full z-10 ">

        <div class="absolute inset-0 flex flex-col justify-between text-white p-6 z-20">
          <div class="flex flex-col">
            <h2 class=" text-slate-50">ARM</h2>
            <p class=" text-slate-50">機械手臂</p>
          </div>
          <div class="w-full flex justify-end items-end">
            <button data-type="icon"
              class="btn-icon group flex items-center justify-center rounded-full bg-white transition-colors duration-300">
              <svg class="w-6 h-6 transform transition-transform duration-1000 group-hover:rotate-[360deg]"
                viewBox="0 0 20 20" fill="url(#grad)" xmlns="http://www.w3.org/2000/svg">
                <defs>
                  <linearGradient id="grad" x1="0%" y1="0%" x2="100%" y2="100%">
                    <stop offset="0%" stop-color="#21DEFF" />
                    <stop offset="100%" stop-color="#E17DFF" />
                  </linearGradient>
                </defs>
                <path d="M10 0L12.3335 7.66655L20 10L12.3335 12.3335L10 20L7.66655 12.3335L0 10L7.66655 7.66655L10 0Z"
                  fill="currentColor" class="fill-[url(#grad)] group-hover: transition-colors duration-500" />
              </svg>
            </button>
          </div>
        </div>
      </div>
    </div>
  </section>

  <section class="save-area mx-auto py-20  bg-slate-900 text-white">
    <!-- title -->
    <div class="text-center space-y-6">
      <h2 class="text-white">深入了解我們如何在不同產業中實現<br class="hidden sm:block">流程自動化、提升運營效率</h2>
      <div class="flex items-center justify-center">
        <p class="text-slate-400">若您的場景需求未列於現有應用中，歡迎</p>
        <button data-type="" class="btn-secondary group active:bg-slate-100">
          <p class="gradient-text transform transition-transform duration-300 group-hover:-translate-x-1 ">聯繫我們</p>
          <svg class="w-5 h-5 transform transition-transform duration-1000 group-hover:rotate-[360deg]"
            viewBox="0 0 20 20" fill="url(#grad)" xmlns="http://www.w3.org/2000/svg">
            <defs>
              <linearGradient id="grad" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" stop-color="#21DEFF" />
                <stop offset="100%" stop-color="#E17DFF" />
              </linearGradient>
            </defs>
            <path d="M10 0L12.3335 7.66655L20 10L12.3335 12.3335L10 20L7.66655 12.3335L0 10L7.66655 7.66655L10 0Z"
              fill="currentColor" class="fill-[url(#grad)] group-hover: transition-colors duration-500" />
          </svg>
        </button>
      </div>
    </div>

    <section class="max-w-6xl mx-auto py-12 grid justify-center items-center grid-cols-3 lg:grid-cols-6 gap-6 px-4 ">
      <a href="/industries.html" class="industry-tab">
        <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" fill="currentColor" class="bi bi-fire"
          viewBox="0 0 16 16">
          <path
            d="M8 16c3.314 0 6-2 6-5.5 0-1.5-.5-4-2.5-6 .25 1.5-1.25 2-1.25 2C11 4 9 .5 6 0c.357 2 .5 4-2 6-1.25 1-2 2.729-2 4.5C2 14 4.686 16 8 16m0-1c-1.657 0-3-1-3-2.75 0-.75.25-2 1.25-3C6.125 10 7 10.5 7 10.5c-.375-1.25.5-3.25 2-3.5-.179 1-.25 2 1 3 .625.5 1 1.364 1 2.25C11 14 9.657 15 8 15" />
        </svg>
        <h5>消防搜救</h5>
      </a>
      <a href="/industries.html" class="industry-tab">
        <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" fill="currentColor" class="bi bi-house-fill"
          viewBox="0 0 16 16">
          <path
            d="M8.707 1.5a1 1 0 0 0-1.414 0L.646 8.146a.5.5 0 0 0 .708.708L8 2.207l6.646 6.647a.5.5 0 0 0 .708-.708L13 5.793V2.5a.5.5 0 0 0-.5-.5h-1a.5.5 0 0 0-.5.5v1.293z" />
          <path d="m8 3.293 6 6V13.5a1.5 1.5 0 0 1-1.5 1.5h-9A1.5 1.5 0 0 1 2 13.5V9.293z" />
        </svg>
        <h5>建築營造</h5>
      </a>
      <a href="/industries.html" class="industry-tab">
        <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" fill="currentColor" class="bi bi-box-seam-fill"
          viewBox="0 0 16 16">
          <path fill-rule="evenodd"
            d="M15.528 2.973a.75.75 0 0 1 .472.696v8.662a.75.75 0 0 1-.472.696l-7.25 2.9a.75.75 0 0 1-.557 0l-7.25-2.9A.75.75 0 0 1 0 12.331V3.669a.75.75 0 0 1 .471-.696L7.443.184l.01-.003.268-.108a.75.75 0 0 1 .558 0l.269.108.01.003zM10.404 2 4.25 4.461 1.846 3.5 1 3.839v.4l6.5 2.6v7.922l.5.2.5-.2V6.84l6.5-2.6v-.4l-.846-.339L8 5.961 5.596 5l6.154-2.461z" />
        </svg>
        <h5>物流倉儲</h5>
      </a>
      <a href="/industries.html" class="industry-tab">
        <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" fill="currentColor" class="bi bi-leaf-fill"
          viewBox="0 0 16 16">
          <path
            d="M1.4 1.7c.217.289.65.84 1.725 1.274 1.093.44 2.885.774 5.834.528 2.02-.168 3.431.51 4.326 1.556C14.161 6.082 14.5 7.41 14.5 8.5q0 .344-.027.734C13.387 8.252 11.877 7.76 10.39 7.5c-2.016-.288-4.188-.445-5.59-2.045-.142-.162-.402-.102-.379.112.108.985 1.104 1.82 1.844 2.308 2.37 1.566 5.772-.118 7.6 3.071.505.8 1.374 2.7 1.75 4.292.07.298-.066.611-.354.715a.7.7 0 0 1-.161.042 1 1 0 0 1-1.08-.794c-.13-.97-.396-1.913-.868-2.77C12.173 13.386 10.565 14 8 14c-1.854 0-3.32-.544-4.45-1.435-1.124-.887-1.889-2.095-2.39-3.383-1-2.562-1-5.536-.65-7.28L.73.806z" />
        </svg>
        <h5>綠色農業</h5>
      </a>
      <a href="/industries.html" class="industry-tab">
        <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" fill="currentColor" class="bi bi-gear-fill"
          viewBox="0 0 16 16">
          <path
            d="M9.405 1.05c-.413-1.4-2.397-1.4-2.81 0l-.1.34a1.464 1.464 0 0 1-2.105.872l-.31-.17c-1.283-.698-2.686.705-1.987 1.987l.169.311c.446.82.023 1.841-.872 2.105l-.34.1c-1.4.413-1.4 2.397 0 2.81l.34.1a1.464 1.464 0 0 1 .872 2.105l-.17.31c-.698 1.283.705 2.686 1.987 1.987l.311-.169a1.464 1.464 0 0 1 2.105.872l.1.34c.413 1.4 2.397 1.4 2.81 0l.1-.34a1.464 1.464 0 0 1 2.105-.872l.31.17c1.283.698 2.686-.705 1.987-1.987l-.169-.311a1.464 1.464 0 0 1 .872-2.105l.34-.1c1.4-.413 1.4-2.397 0-2.81l-.34-.1a1.464 1.464 0 0 1-.872-2.105l.17-.31c.698-1.283-.705-2.686-1.987-1.987l-.311.169a1.464 1.464 0 0 1-2.105-.872zM8 10.93a2.929 2.929 0 1 1 0-5.86 2.929 2.929 0 0 1 0 5.858z" />
        </svg>
        <h5>生產製造</h5>
      </a>
      <a href="/industries.html" class="industry-tab">
          <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" fill="currentColor" class="bi bi-capsule" viewBox="0 0 16 16">
          <path d="M1.828 8.9 8.9 1.827a4 4 0 1 1 5.657 5.657l-7.07 7.071A4 4 0 1 1 1.827 8.9Zm9.128.771 2.893-2.893a3 3 0 1 0-4.243-4.242L6.713 5.429z"/>
          </svg>
        <h5>生產製造</h5>
      </a>
    </section>

  </section>

  <section class="save-area mx-auto py-20 ">
    <div class="text-center space-y-6 mb-20">
      <h2>最新消息</h2>
      <div class="flex items-center justify-center">
        <p>關注我們的活動、都在</p>
        <button data-type="" class="btn-secondary group active:bg-slate-100">
          <p class="gradient-text transform transition-transform duration-300 group-hover:-translate-x-1 ">新聞中心</p>
          <svg class="w-5 h-5 transform transition-transform duration-1000 group-hover:rotate-[360deg]"
            viewBox="0 0 20 20" fill="url(#grad)" xmlns="http://www.w3.org/2000/svg">
            <defs>
              <linearGradient id="grad" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" stop-color="#21DEFF"/>
                <stop offset="100%" stop-color="#E17DFF" />
              </linearGradient>
            </defs>
            <path d="M10 0L12.3335 7.66655L20 10L12.3335 12.3335L10 20L7.66655 12.3335L0 10L7.66655 7.66655L10 0Z"
              fill="currentColor" class="fill-[url(#grad)] group-hover: transition-colors duration-500" />
          </svg>
        </button>
      </div>
    </div>

    <div>

      <div class="flex flex-nowrap overflow-x-auto mx-auto pb-20  gap-8 px-4">

        <!-- 卡片 1 -->
        <article class="w-96 shrink-0 rounded-3xl border border-slate-200 transition overflow-hidden p-4 hover:bg-white hover:shadow-xl">
          <div class="relative">
            <img src="/assets/img/news1.jpg" alt="文章封面" class="w-full h-52 object-cover rounded-2xl">
            <small class="absolute bottom-2 left-2 rounded-full bg-blue-100 text-blue-600  px-3 py-1">技術</small>
          </div>
          <div class="flex flex-col justify-between ">
            <div class="py-4">
              <h5 class="font-bold my-2">仿生機器人登場，智慧模擬人類行為引關注</h5>
              <small>2025-06-15</small>
              <p class="line-clamp-4">
                仿生機器人首度公開亮相，現場示範自學與環境適應能力，展現其在陪護、教育與服務領域中的多元應用潛力。透過深度強化學習與仿真訓練，這款機器人能自然理解使用者情緒並回應，無縫融入日常互動場景。現場觀眾更見證它在不熟悉環境中自主學習避障與人臉識別，未來將在家庭陪護、智慧教室及客服導覽等場域大放異彩。
              </p>
            </div>
            <div class="flex justify-end">
              <button data-type="" class="btn-secondary group active:bg-slate-100">
                <p class="gradient-text transform transition-transform duration-300 group-hover:-translate-x-1 ">閱讀文章</p>
                <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" fill="currentColor"
                class="w-6 h-6 transform transition-transform duration-1000" viewBox="0 0 16 16">
                <path fill-rule="evenodd"
                  d="M4 8a.5.5 0 0 1 .5-.5h5.793L8.146 5.354a.5.5 0 1 1 .708-.708l3 3a.5.5 0 0 1 0 .708l-3 3a.5.5 0 0 1-.708-.708L10.293 8.5H4.5A.5.5 0 0 1 4 8"
                  class="fill-[url(#grad)] group-hover:transition-colors duration-500" />
                </svg>
              </button>
            </div>
          </div>
        </article>

        <!-- 卡片 2 -->
        <article class="w-96 shrink-0 rounded-3xl border border-slate-200 transition overflow-hidden p-4 hover:bg-white hover:shadow-xl">
          <div class="relative">
            <img src="/assets/img/news2.jpg" alt="文章封面" class="w-full h-52 object-cover rounded-2xl">
            <small class="absolute bottom-2 left-2 rounded-full bg-pink-100 text-pink-600 px-3 py-1">活動</small>
          </div>
          <div class="flex flex-col justify-between ">
            <div class="py-4">
              <h5 class="font-bold my-2">大型機械手臂第二代發表會將於9月登場，智慧與性能全面升級</h5>
              <small>2025-04-26</small>
              <p class="line-clamp-4">
                仿生機器人首度公開亮相，現場示範自學與環境適應能力，展現其在陪護、教育與服務領域中的多元應用潛力。透過深度強化學習與仿真訓練，這款機器人能自然理解使用者情緒並回應，無縫融入日常互動場景。現場觀眾更見證它在不熟悉環境中自主學習避障與人臉識別，未來將在家庭陪護、智慧教室及客服導覽等場域大放異彩。
              </p>
            </div>
            <div class="flex justify-end">
              <button data-type="" class="btn-secondary group active:bg-slate-100">
                <p class="gradient-text transform transition-transform duration-300 group-hover:-translate-x-1 ">閱讀文章</p>
                <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" fill="currentColor"
                class="w-6 h-6 transform transition-transform duration-1000" viewBox="0 0 16 16">
                <path fill-rule="evenodd"
                  d="M4 8a.5.5 0 0 1 .5-.5h5.793L8.146 5.354a.5.5 0 1 1 .708-.708l3 3a.5.5 0 0 1 0 .708l-3 3a.5.5 0 0 1-.708-.708L10.293 8.5H4.5A.5.5 0 0 1 4 8"
                  class="fill-[url(#grad)] group-hover:transition-colors duration-500" />
                </svg>
              </button>
            </div>
          </div>
        </article>
        <!-- 卡片 3 -->
        <article class="w-96 shrink-0 rounded-3xl border border-slate-200 transition overflow-hidden p-4 hover:bg-white hover:shadow-xl">
          <div class="relative">
            <img src="/assets/img/news3.jpg" alt="文章封面" class="w-full h-52 object-cover rounded-2xl">
            <small class="absolute bottom-2 left-2 rounded-full bg-orange-100 text-orange-600 px-3 py-1">焦點</small>
          </div>
          <div class="flex flex-col justify-between ">
            <div class="py-4">
              <h5 class="font-bold my-2">醫療機器人突破，實現微創手術新里程碑</h5>
              <small>2025-02-12</small>
              <p class="line-clamp-4">
                醫療領域再創突破，最新醫療級機器人系統 Focalist 完成標準化微創手術示範，配備即時超音波影像導引與精準針具控制功能，可實現高重現性、安全性與效率的手術操作 。該系統已取得 FDA 批准，未來將加速進入臨床實驗階段，預期臨床應用將縮短患者恢復時間，並提升醫療團隊的作業穩定度。
              </p>
            </div>
            <div class="flex justify-end">
              <button data-type="" class="btn-secondary group active:bg-slate-100">
                <p class="gradient-text transform transition-transform duration-300 group-hover:-translate-x-1 ">閱讀文章</p>
                <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" fill="currentColor"
                class="w-6 h-6 transform transition-transform duration-1000" viewBox="0 0 16 16">
                <path fill-rule="evenodd"
                  d="M4 8a.5.5 0 0 1 .5-.5h5.793L8.146 5.354a.5.5 0 1 1 .708-.708l3 3a.5.5 0 0 1 0 .708l-3 3a.5.5 0 0 1-.708-.708L10.293 8.5H4.5A.5.5 0 0 1 4 8"
                  class="fill-[url(#grad)] group-hover:transition-colors duration-500" />
                </svg>
              </button>
            </div>
          </div>
        </article>

      </div>
    </div>
  </section>

  <section class="save-area mx-auto mb-32">
    <div class="flex flex-col lg:flex-row items-stretch w-full bg-white rounded-3xl">
      <div class="lg:w-1/2 w-full">
        <img src="/assets/img/contact-us.jpg" alt="" class="lg:flex-1 object-cover rounded-3xl w-full h-full">
      </div>
      <div class="lg:w-1/2 space-y-4 p-8 flex flex-col justify-center">
        <h3 class="">馬上了解我們的解決方案</h3>
        <p class="">
          我們的專家隨時準備幫助您規劃自動化的成功之路。他們可以為您解答所有與 Altron 相關的問題，包括:
        </p>
        <div class="flex flex-col gap-y-1">
          <div class="flex gap-x-4 ">
              <img class=" " src="/assets/icon/Check2Circle.svg" alt="">
              <p class=" ">有關我們產品組合和價格的資訊</p>
          </div>
          <div class="flex gap-x-4">
              <img class="" src="/assets/icon/Check2Circle.svg" alt="">
              <p class=" ">符合您業務需求的定制解決方案</p>
          </div>
          <div class="flex gap-x-4">
              <img class=" " src="/assets/icon/Check2Circle.svg" alt="">
              <p class="">經銷商合作</p>
          </div>
        </div>
        <div class="flex justify-end gap-x-2 pt-6">
          <button class="btn-primary self-start">預約諮詢</button>
        </div>
      </div>
    </div>
  </section>

  <footer id="footer"></footer>

  <script src="/src/js/typing.js"></script>
  <script src="/src/js/counting.js"></script>
  <script src="/src/js/card.js"></script>

</body>

</html>