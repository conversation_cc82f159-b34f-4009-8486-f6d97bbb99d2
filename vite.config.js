import { defineConfig } from 'vite'
import path from 'path';


export default defineConfig({
  
  build: {
    rollupOptions: {
      input: {
        main: path.resolve(__dirname, 'index.html'), // 預設首頁
        about: path.resolve(__dirname, 'public/about.html'),
        industries: path.resolve(__dirname, 'public/industries.html'),
        products: path.resolve(__dirname, 'public/products.html'),
        support: path.resolve(__dirname, 'public/support.html'),
        contact: path.resolve(__dirname, 'public/contact.html'),

      }
    }
  },
  base: '/altron-v2/',
});
