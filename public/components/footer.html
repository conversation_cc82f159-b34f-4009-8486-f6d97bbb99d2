
<div class="save-area m-auto py-20">
  <!-- <a href="#" class="fixed bottom-10 right-10 z-[1000] w-12 h-12 overflow-hidden rounded-full backdrop-blur">
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" class="size-6 absolute w-10 h-10 rounded-full">
      <path d="M4.913 2.658c2.075-.27 4.19-.408 6.337-.408 2.147 0 4.262.139 6.337.408 1.922.25 3.291 1.861 3.405 3.727a4.403 4.403 0 0 0-1.032-.211 50.89 50.89 0 0 0-8.42 0c-2.358.196-4.04 2.19-4.04 4.434v4.286a4.47 4.47 0 0 0 2.433 3.984L7.28 21.53A.75.75 0 0 1 6 21v-4.03a48.527 48.527 0 0 1-1.087-.128C2.905 16.58 1.5 14.833 1.5 12.862V6.638c0-1.97 1.405-3.718 3.413-3.979Z" />
      <path d="M15.75 7.5c-1.376 0-2.739.057-4.086.169C10.124 7.797 9 9.103 9 10.609v4.285c0 1.507 1.128 2.814 2.67 2.94 1.243.102 2.5.157 3.768.165l2.782 2.781a.75.75 0 0 0 1.28-.53v-2.39l.33-.026c1.542-.125 2.67-1.433 2.67-2.94v-4.286c0-1.505-1.125-2.811-2.664-2.94A49.392 49.392 0 0 0 15.75 7.5Z" />
    </svg>
  </a> -->
  <div class="w-full flex flex-col justify-between mb-8 lg:flex-row">
    <div class="flex flex-col gap-6">
      <div class="flex items-center gap-4">
        <img src="/src/assets/icon/logo-icon-color.svg" alt="">
        <div>
          <img src="/src/assets/icon/logo-altron-w.svg" alt="">
          <small class="slogan">Infinite Possibilities, Seamlessly Engineered.</small>
        </div>
      </div>
      <div class="flex gap-4">
        <!-- ig mail x youtube fb -->
        <button></button>
        <button></button>
        <button></button>
        <button></button>
        <button></button>
      </div>
    </div>
    <div class="flex flex-col gap-16 md:flex-row ">
      <div class="flex flex-row gap-16">
        <div class="flex flex-col gap-6">
          <p class="text-slate-400">關於我們</p>
          <div class="flex flex-col gap-2">
            <a href="/about.html" class="text-white hover:gradient-text">關於Altron</a>
            <a href="#" class="text-white hover:gradient-text">新聞中心</a>
          </div>
        </div>
        <div class="flex flex-col gap-6">
          <p class="text-slate-400">產品</p>
          <div class="flex flex-col gap-2">
            <a href="/products.html" class="text-white hover:gradient-text">HRC 協作人形</a>
            <a href="products.html"  class="text-white hover:gradient-text">HAC 自主移動人形</a>
            <a href="/products.html"  class="text-white hover:gradient-text">CAB 自主移動生物</a>
            <a href="/products.html"  class="text-white hover:gradient-text">ARM 機械手臂</a>
          </div>
        </div>
      </div>
      <div class="flex flex-row gap-16">
        <div class="flex flex-col gap-6">
          <p class="text-slate-400">應用</p>
          <div class="flex flex-col gap-2 ">
            <a href="#" class="text-white hover:gradient-text">工業自動化</a>
            <a href="#" class="text-white hover:gradient-text">醫療健康</a>
            <a href="#" class="text-white hover:gradient-text">農業科技</a>
            <a href="#" class="text-white hover:gradient-text">物流運輸</a>
            <a href="#" class="text-white hover:gradient-text">物流運輸</a>
          </div>
        </div>
        <div class="flex flex-col gap-6">
          <p class="text-slate-400">支援</p>
          <div class="flex flex-col gap-2">
            <a href="#" class="text-white hover:gradient-text">技術支援</a>
            <a href="#" class="text-white hover:gradient-text">常見問題</a>
            <a href="#" class="text-white hover:gradient-text">聯繫我們</a>
          </div>
        </div>
      </div>

    </div>
  </div>
  <div class="flex flex-col-reverse lg:flex-row justify-between gap-6 pt-28">
    <small class="text-slate-400">本網站為緯育TibaMe [第94期]前端工程師專業技術養成班學員作品，僅供學習、展示之用途。</small>
    <div class="flex gap-6">
      <a href="#" class="text-slate-400">隱私權政策</a>
      <a href="#" class="text-slate-400">Cookie政策</a>
    </div>
  </div>
</div>
